{% extends 'base.html' %}

{% block title %}首页 - {{ super() }}{% endblock %}

{% block extra_css %}
<style>
/* 全局动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

/* 英雄区域 */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    color: white;
    padding: 100px 0;
    margin-bottom: 50px;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 6s ease-in-out infinite;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    animation: fadeInUp 1s ease-out;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-section p {
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-section .btn {
    animation: fadeInUp 1s ease-out 0.4s both;
    transition: all 0.3s ease;
}

.hero-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

/* 功能亮点卡片 */
.feature-highlight {
    text-align: center;
    padding: 40px 25px;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0,0,0,0.1),
        0 1px 3px rgba(0,0,0,0.1);
    height: 100%;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.2);
}

.feature-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.feature-highlight:hover::before {
    left: 100%;
}

.feature-highlight:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 30px 60px rgba(0,0,0,0.15),
        0 5px 15px rgba(0,0,0,0.1);
}

.feature-icon {
    font-size: 3.5rem;
    margin-bottom: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.feature-highlight:hover .feature-icon {
    animation: pulse 1s ease-in-out;
}

.feature-highlight h5 {
    font-weight: 600;
    margin-bottom: 15px;
    color: #2c3e50;
}

.feature-highlight .badge {
    margin: 2px;
    padding: 5px 12px;
    font-size: 0.75rem;
    border-radius: 20px;
}

/* 小功能项 */
.feature-item {
    text-align: center;
    padding: 25px 20px;
    background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    height: 100%;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.feature-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-item:hover::after {
    transform: scaleX(1);
}

.feature-item:hover {
    background: linear-gradient(145deg, #ffffff 0%, #f1f3f4 100%);
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.feature-item i {
    font-size: 2.2rem;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.feature-item:hover i {
    transform: scale(1.1);
}

.feature-item h6 {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

/* 统计区域 */
.stats-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 80px 0;
    margin: 60px 0;
    position: relative;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.3)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.stat-item {
    text-align: center;
    padding: 30px 20px;
    position: relative;
    z-index: 2;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.stat-item:hover .stat-number {
    transform: scale(1.1);
}

.stat-label {
    color: #5a6c7d;
    font-size: 1.1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* 紫色主题 */
.text-purple {
    color: #6f42c1 !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
    }

    .feature-highlight {
        margin-bottom: 30px;
    }

    .feature-icon {
        font-size: 2.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

/* 加载动画 */
.container > .row {
    animation: fadeInUp 0.8s ease-out;
}

.container > .row:nth-child(2) {
    animation-delay: 0.2s;
}

.container > .row:nth-child(3) {
    animation-delay: 0.4s;
}

/* 按钮增强 */
.btn-lg {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* 标题增强 */
h2.display-5 {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}
</style>
{% endblock %}

{% block content %}
<div class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto text-center">
                <h1 class="display-4 mb-4">校园餐智慧食堂监管平台<br><small class="text-light">(Smart Canteen Management Platform)</small></h1>
                <p class="lead mb-5">全方位智能化食堂管理解决方案，让食品安全看得见、管得住、可追溯</p>

                <div class="d-flex justify-content-center gap-3 mb-4">
                    <a href="{{ url_for('auth.login') }}" class="btn btn-light btn-lg px-5">
                        <i class="fas fa-sign-in-alt me-2"></i>立即体验
                    </a>
                    <a href="#features" class="btn btn-outline-light btn-lg px-5">
                        <i class="fas fa-info-circle me-2"></i>了解更多
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 核心亮点功能展示 -->
<div id="features" class="container">
    <div class="row text-center mb-5">
        <div class="col-12">
            <h2 class="display-5 mb-3">🌟 核心功能亮点</h2>
            <p class="lead text-muted">八大智能化功能，全面保障食堂安全管理</p>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="feature-highlight">
                <div class="feature-icon">
                    <i class="fas fa-qrcode text-primary"></i>
                </div>
                <h5>智能检查系统</h5>
                <p class="text-muted">员工扫码上传食堂卫生、设备情况，管理员在线评价，实时监控运营状态</p>
                <div class="mt-3">
                    <span class="badge bg-primary">扫码上传</span>
                    <span class="badge bg-success">在线评价</span>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="feature-highlight">
                <div class="feature-icon">
                    <i class="fas fa-users text-success"></i>
                </div>
                <h5>家校共陪餐</h5>
                <p class="text-muted">家长参与陪餐体验，透明化食堂管理，增强家校信任</p>
                <div class="mt-3">
                    <span class="badge bg-success">透明管理</span>
                    <span class="badge bg-info">家校互动</span>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="feature-highlight">
                <div class="feature-icon">
                    <i class="fas fa-file-alt text-info"></i>
                </div>
                <h5>智能日志生成</h5>
                <p class="text-muted">每天自动生成工作日志，全面记录食堂运营情况，数据化管理决策</p>
                <div class="mt-3">
                    <span class="badge bg-info">自动生成</span>
                    <span class="badge bg-warning">数据分析</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 其他核心功能 -->
    <div class="row mb-5">
        <div class="col-md-3 mb-3">
            <div class="feature-item">
                <i class="fas fa-calendar-alt text-warning"></i>
                <h6>灵活菜单管理</h6>
                <small>周菜单灵活安排，支持打印，一键导入采购计划</small>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="feature-item">
                <i class="fas fa-shopping-cart text-danger"></i>
                <h6>智能采购系统</h6>
                <small>灵活选择供应商，智能价格对比，自动生成采购单</small>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="feature-item">
                <i class="fas fa-warehouse text-purple"></i>
                <h6>出入库管理</h6>
                <small>完整流程管理，自动生成台账报表，库存实时监控</small>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="feature-item">
                <i class="fas fa-search text-dark"></i>
                <h6>全程溯源</h6>
                <small>食品全程可追溯，供应链透明化，安全责任到人</small>
            </div>
        </div>
    </div>

    <!-- 特色功能亮点 -->
    <div class="row mb-5">
        <div class="col-md-6 mb-3">
            <div class="feature-item bg-light">
                <i class="fas fa-print text-primary"></i>
                <h6>一键式留样标签打印</h6>
                <small>规范化留样流程，自动生成标签，确保食品安全</small>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="feature-item bg-light">
                <i class="fas fa-chart-line text-success"></i>
                <h6>数据分析决策</h6>
                <small>智能数据分析，帮助管理者做出科学决策</small>
            </div>
        </div>
    </div>
</div>

<!-- 统计数据展示 -->
<div class="stats-section">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">食品安全追溯</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">实时监控</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">智能化</div>
                    <div class="stat-label">管理流程</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">透明化</div>
                    <div class="stat-label">家校共管</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 行动号召 -->
<div class="container text-center mb-5">
    <h3 class="mb-4">开始您的智慧食堂管理之旅</h3>
    <p class="lead text-muted mb-4">让食品安全管理更简单、更智能、更透明</p>
    <a href="{{ url_for('auth.login') }}" class="btn btn-primary btn-lg px-5 me-3">
        <i class="fas fa-rocket me-2"></i>立即开始
    </a>
    <a href="{{ url_for('auth.register') }}" class="btn btn-outline-primary btn-lg px-5">
        <i class="fas fa-user-plus me-2"></i>注册账号
    </a>
</div>
{% endblock %}
