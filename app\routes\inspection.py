from flask import render_template, redirect, url_for, flash, request, current_app, abort, jsonify
from flask_login import login_required, current_user
from app import db
from app.models_daily_management import InspectionRecord, DailyLog, Photo
from app.models import PurchaseOrder, StockIn
from datetime import datetime, date, timedelta
from sqlalchemy import text
import os
from werkzeug.utils import secure_filename
from PIL import Image
from flask import Blueprint

inspection_bp = Blueprint('inspection', __name__)

# 入库检查首页
@inspection_bp.route('/')
@login_required
def index():
    """入库检查首页"""
    # 简化权限设置，获取所有待检查的采购订单
    orders = PurchaseOrder.query.filter(
        PurchaseOrder.status.in_(['已审核', '待入库', '已确认'])
    ).order_by(PurchaseOrder.order_date.desc()).all()

    return render_template('inspection/direct_index.html',
                          title='入库检查',
                          orders=orders)

@inspection_bp.route('/quick-pass', methods=['POST'])
@login_required
def quick_pass():
    """快速通过检查"""
    try:
        # 获取JSON数据
        data = request.get_json()
        purchase_order_id = data.get('purchase_order_id')

        if not purchase_order_id:
            return jsonify({'success': False, 'message': '缺少订单ID参数'})

        # 获取采购订单
        purchase_order = PurchaseOrder.query.get_or_404(purchase_order_id)

        # 获取或创建今日日志
        today_date = date.today()
        log = DailyLog.query.filter_by(log_date=today_date).first()

        if not log:
            try:
                # 使用原始SQL创建日志
                sql = text("""
                INSERT INTO daily_logs
                (log_date, manager, student_count, teacher_count, other_count, created_by)
                OUTPUT inserted.id
                VALUES
                (CONVERT(DATE, :log_date, 23), :manager, :student_count, :teacher_count, :other_count, :created_by)
                """)

                # 准备参数
                params = {
                    'log_date': today_date.strftime('%Y-%m-%d'),
                    'manager': current_user.real_name,
                    'student_count': 0,
                    'teacher_count': 0,
                    'other_count': 0,
                    'created_by': current_user.id
                }

                # 执行SQL
                result = db.session.execute(sql, params)
                log_id = result.fetchone()[0]
                db.session.commit()

                # 获取新创建的日志
                log = DailyLog.query.get(log_id)
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"创建日志失败: {str(e)}")
                return jsonify({'success': False, 'message': f'创建日志失败: {str(e)}'})

        # 创建检查记录
        try:
            # 使用原始SQL创建检查记录
            sql = text("""
            INSERT INTO inspection_records
            (daily_log_id, inspection_type, inspection_item, status, description, reference_type, reference_id, inspector_id)
            OUTPUT inserted.id
            VALUES
            (:daily_log_id, :inspection_type, :inspection_item, :status, :description, :reference_type, :reference_id, :inspector_id)
            """)

            # 准备参数
            params = {
                'daily_log_id': log.id,
                'inspection_type': 'morning',  # 临时使用morning类型，避免约束冲突
                'inspection_item': f'采购订单 #{purchase_order.order_number} 入库检查',
                'status': 'normal',
                'description': f'检查通过，采购订单 #{purchase_order.order_number} 的入库物品',
                'reference_type': 'purchase_order',
                'reference_id': purchase_order.id,
                'inspector_id': current_user.id
            }

            # 执行SQL
            result = db.session.execute(sql, params)
            inspection_id = result.fetchone()[0]

            # 创建入库记录
            stock_in = StockIn(
                purchase_order_id=purchase_order.id,
                warehouse_id=purchase_order.warehouse_id,
                status='已入库',
                notes=f'通过入库检查 #{inspection_id}',
                created_by=current_user.id
            )
            db.session.add(stock_in)

            # 更新采购订单状态
            purchase_order.status = '已入库'

            db.session.commit()

            return jsonify({'success': True, 'message': '检查通过，订单已标记为已入库'})
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建检查记录失败: {str(e)}")
            return jsonify({'success': False, 'message': f'创建检查记录失败: {str(e)}'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

# 原始入库检查首页
@inspection_bp.route('/original')
@login_required
def original_index():
    """原始入库检查首页"""
    # 获取最近的检查记录 - 临时使用morning类型查询入库检查
    recent_inspections = InspectionRecord.query.filter(
        InspectionRecord.inspection_item.like('%入库检查%')
    ).order_by(InspectionRecord.created_at.desc()).limit(10).all()

    # 获取待检查的采购订单
    pending_orders = PurchaseOrder.query.filter_by(
        status='已确认'
    ).order_by(PurchaseOrder.created_at.desc()).limit(5).all()

    return render_template('inspection/index.html',
                          title='入库检查管理',
                          recent_inspections=recent_inspections,
                          pending_orders=pending_orders)

# 创建入库检查记录
@inspection_bp.route('/create/<int:purchase_order_id>')
@login_required
def create(purchase_order_id):
    """创建入库检查记录"""
    # 获取采购订单
    purchase_order = PurchaseOrder.query.get_or_404(purchase_order_id)

    # 检查是否已经有检查记录
    # 使用原始SQL查询，避免使用不存在的字段
    from sqlalchemy import text
    sql = text("""
    SELECT id FROM inspection_records
    WHERE inspection_item LIKE :item
    """)
    result = db.session.execute(sql, {'item': f'%采购订单 #{purchase_order_id}%'})
    existing_inspection_id = result.scalar()
    existing_inspection = InspectionRecord.query.get(existing_inspection_id) if existing_inspection_id else None

    if existing_inspection:
        return redirect(url_for('inspection.edit', id=existing_inspection.id))

    # 获取或创建今日日志
    today_date = date.today()
    log = DailyLog.query.filter_by(log_date=today_date).first()

    if not log:
        try:
            # 使用原始SQL创建日志，避免时间戳问题
            sql = text("""
            INSERT INTO daily_logs
            (log_date, manager, student_count, teacher_count, other_count, created_by)
            OUTPUT inserted.id
            VALUES
            (CONVERT(DATE, :log_date, 23), :manager, :student_count, :teacher_count, :other_count, :created_by)
            """)

            # 准备参数
            params = {
                'log_date': today_date.strftime('%Y-%m-%d'),
                'manager': current_user.real_name,
                'student_count': 0,
                'teacher_count': 0,
                'other_count': 0,
                'created_by': current_user.id
            }

            # 执行SQL
            result = db.session.execute(sql, params)
            log_id = result.fetchone()[0]
            db.session.commit()

            # 获取新创建的日志
            log = DailyLog.query.get(log_id)
            flash('已自动创建今日工作日志', 'info')
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建日志失败: {str(e)}")
            flash(f'创建日志失败: {str(e)}', 'danger')
            return redirect(url_for('inspection.index'))

    # 创建检查记录
    try:
        # 使用原始SQL创建检查记录
        sql = text("""
        INSERT INTO inspection_records
        (daily_log_id, inspection_type, inspection_item, status, description, reference_type, reference_id, inspector_id)
        OUTPUT inserted.id
        VALUES
        (:daily_log_id, :inspection_type, :inspection_item, :status, :description, :reference_type, :reference_id, :created_by)
        """)

        # 准备参数
        params = {
            'daily_log_id': log.id,
            'inspection_type': 'morning',  # 临时使用morning类型，避免约束冲突
            'inspection_item': f'采购订单 #{purchase_order.id} 入库检查',
            'status': 'pending',
            'description': f'检查采购订单 #{purchase_order.id} 的入库物品',
            'reference_type': 'purchase_order',
            'reference_id': purchase_order.id,
            'created_by': current_user.id
        }

        # 执行SQL
        result = db.session.execute(sql, params)
        inspection_id = result.fetchone()[0]
        db.session.commit()

        flash('已创建入库检查记录', 'success')
        return redirect(url_for('inspection.edit', id=inspection_id))
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建检查记录失败: {str(e)}")
        flash(f'创建检查记录失败: {str(e)}', 'danger')
        return redirect(url_for('inspection.index'))

# 编辑入库检查记录
@inspection_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑入库检查记录"""
    inspection = InspectionRecord.query.get_or_404(id)

    # 获取关联的采购订单
    purchase_order = None
    if inspection.reference_type == 'purchase_order':
        purchase_order = PurchaseOrder.query.get(inspection.reference_id)

    if request.method == 'POST':
        try:
            # 更新检查记录
            inspection.status = request.form.get('status')
            inspection.description = request.form.get('description')

            # 如果检查通过，创建入库记录
            if inspection.status == 'normal' and purchase_order:
                # 创建入库记录
                stock_in = StockIn(
                    purchase_order_id=purchase_order.id,
                    warehouse_id=purchase_order.warehouse_id,
                    status='已入库',
                    notes=f'通过入库检查 #{inspection.id}',
                    created_by=current_user.id
                )
                db.session.add(stock_in)

                # 更新采购订单状态
                purchase_order.status = '已入库'

            db.session.commit()
            flash('检查记录已更新', 'success')

            # 处理照片上传
            if 'photos' in request.files:
                photos = request.files.getlist('photos')
                for photo in photos:
                    if photo and photo.filename:
                        try:
                            # 保存照片
                            filename = secure_filename(photo.filename)
                            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                            new_filename = f"inspection_{timestamp}_{filename}"

                            # 确保目录存在
                            upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'inspections')
                            os.makedirs(upload_folder, exist_ok=True)

                            # 保存文件
                            file_path = os.path.join(upload_folder, new_filename)
                            photo.save(file_path)

                            # 创建照片记录
                            photo_record = Photo(
                                file_path=f"/static/uploads/inspections/{new_filename}",
                                reference_type='inspection',
                                reference_id=inspection.id,
                                rating=request.form.get('rating', type=int) or 0,
                                created_by=current_user.id
                            )
                            db.session.add(photo_record)
                        except Exception as e:
                            current_app.logger.error(f"保存照片失败: {str(e)}")
                            flash(f'保存照片失败: {str(e)}', 'warning')

                db.session.commit()

            return redirect(url_for('inspection.view', id=inspection.id))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新检查记录失败: {str(e)}")
            flash(f'更新检查记录失败: {str(e)}', 'danger')

    # 获取照片
    photos = []
    try:
        # 使用原始SQL查询获取照片
        sql = text("""
        SELECT id, file_path, rating, upload_time
        FROM photos
        WHERE reference_type = 'inspection' AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {'reference_id': inspection.id})

        for row in result:
            photos.append({
                'id': row[0],
                'file_path': row[1],
                'rating': row[2],
                'upload_time': row[3]
            })
    except Exception as e:
        current_app.logger.error(f"获取照片失败: {str(e)}")

    return render_template('inspection/edit.html',
                          title='编辑入库检查',
                          inspection=inspection,
                          purchase_order=purchase_order,
                          photos=photos)

# 查看入库检查记录
@inspection_bp.route('/view/<int:id>')
@login_required
def view(id):
    """查看入库检查记录"""
    inspection = InspectionRecord.query.get_or_404(id)

    # 获取关联的采购订单
    purchase_order = None
    if inspection.reference_type == 'purchase_order':
        purchase_order = PurchaseOrder.query.get(inspection.reference_id)

    # 获取照片
    photos = []
    try:
        # 使用原始SQL查询获取照片
        sql = text("""
        SELECT id, file_path, rating, upload_time
        FROM photos
        WHERE reference_type = 'inspection' AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {'reference_id': inspection.id})

        for row in result:
            photos.append({
                'id': row[0],
                'file_path': row[1],
                'rating': row[2],
                'upload_time': row[3]
            })
    except Exception as e:
        current_app.logger.error(f"获取照片失败: {str(e)}")

    return render_template('inspection/view.html',
                          title='查看入库检查',
                          inspection=inspection,
                          purchase_order=purchase_order,
                          photos=photos)

# 保存简化版入库检查结果
@inspection_bp.route('/save-inspection', methods=['POST'])
@login_required
def save_inspection():
    """保存简化版入库检查结果"""
    try:
        # 获取表单数据
        purchase_order_id = request.form.get('purchase_order_id', type=int)
        status = request.form.get('status')
        description = request.form.get('description')

        if not purchase_order_id or not status:
            flash('缺少必要参数', 'danger')
            return redirect(url_for('inspection.index'))

        # 获取采购订单
        purchase_order = PurchaseOrder.query.get_or_404(purchase_order_id)

        # 获取或创建今日日志
        today_date = date.today()
        log = DailyLog.query.filter_by(log_date=today_date).first()

        if not log:
            try:
                # 使用原始SQL创建日志
                sql = text("""
                INSERT INTO daily_logs
                (log_date, manager, student_count, teacher_count, other_count, created_by)
                OUTPUT inserted.id
                VALUES
                (CONVERT(DATE, :log_date, 23), :manager, :student_count, :teacher_count, :other_count, :created_by)
                """)

                # 准备参数
                params = {
                    'log_date': today_date.strftime('%Y-%m-%d'),
                    'manager': current_user.real_name,
                    'student_count': 0,
                    'teacher_count': 0,
                    'other_count': 0,
                    'created_by': current_user.id
                }

                # 执行SQL
                result = db.session.execute(sql, params)
                log_id = result.fetchone()[0]
                db.session.commit()

                # 获取新创建的日志
                log = DailyLog.query.get(log_id)
                flash('已自动创建今日工作日志', 'info')
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"创建日志失败: {str(e)}")
                flash(f'创建日志失败: {str(e)}', 'danger')
                return redirect(url_for('inspection.index'))

        # 检查是否已经有检查记录
        sql = text("""
        SELECT id FROM inspection_records
        WHERE inspection_item LIKE :item
        """)
        result = db.session.execute(sql, {'item': f'%采购订单 #{purchase_order_id}%'})
        existing_inspection_id = result.scalar()

        inspection_id = None

        if existing_inspection_id:
            # 更新现有检查记录
            inspection = InspectionRecord.query.get(existing_inspection_id)
            inspection.status = status
            inspection.description = description
            inspection_id = inspection.id
            flash('检查记录已更新', 'success')
        else:
            # 创建新的检查记录
            sql = text("""
            INSERT INTO inspection_records
            (daily_log_id, inspection_type, inspection_item, status, description, reference_type, reference_id, inspector_id)
            OUTPUT inserted.id
            VALUES
            (:daily_log_id, :inspection_type, :inspection_item, :status, :description, :reference_type, :reference_id, :inspector_id)
            """)

            # 准备参数
            params = {
                'daily_log_id': log.id,
                'inspection_type': 'morning',  # 临时使用morning类型，避免约束冲突
                'inspection_item': f'采购订单 #{purchase_order.id} 入库检查',
                'status': status,
                'description': description or f'检查采购订单 #{purchase_order.id} 的入库物品',
                'reference_type': 'purchase_order',
                'reference_id': purchase_order.id,
                'inspector_id': current_user.id
            }

            # 执行SQL
            result = db.session.execute(sql, params)
            inspection_id = result.fetchone()[0]
            flash('检查记录已创建', 'success')

        # 如果检查通过，创建入库记录并更新采购订单状态
        if status == 'normal':
            # 创建入库记录
            stock_in = StockIn(
                purchase_order_id=purchase_order.id,
                warehouse_id=purchase_order.warehouse_id,
                status='已入库',
                notes=f'通过入库检查 #{inspection_id}',
                created_by=current_user.id
            )
            db.session.add(stock_in)

            # 更新采购订单状态
            purchase_order.status = '已入库'

            flash('采购订单已标记为已入库', 'success')

        # 处理照片上传
        if 'photos' in request.files:
            photos = request.files.getlist('photos')
            for photo in photos:
                if photo and photo.filename:
                    try:
                        # 保存照片
                        filename = secure_filename(photo.filename)
                        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                        new_filename = f"inspection_{timestamp}_{filename}"

                        # 确保目录存在
                        upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'inspections')
                        os.makedirs(upload_folder, exist_ok=True)

                        # 保存文件
                        file_path = os.path.join(upload_folder, new_filename)
                        photo.save(file_path)

                        # 创建照片记录
                        photo_record = Photo(
                            file_path=f"/static/uploads/inspections/{new_filename}",
                            reference_type='inspection',
                            reference_id=inspection_id,
                            rating=3,  # 默认评分
                            file_name=filename
                        )
                        db.session.add(photo_record)
                    except Exception as e:
                        current_app.logger.error(f"保存照片失败: {str(e)}")
                        flash(f'保存照片失败: {str(e)}', 'warning')

        db.session.commit()

        if inspection_id:
            return redirect(url_for('inspection.view', id=inspection_id))
        else:
            return redirect(url_for('inspection.index'))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"保存检查记录失败: {str(e)}")
        flash(f'保存检查记录失败: {str(e)}', 'danger')
        return redirect(url_for('inspection.index'))
