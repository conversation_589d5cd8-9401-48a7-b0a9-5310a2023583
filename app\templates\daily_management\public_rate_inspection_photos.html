{% extends 'base_public.html' %}

{% block title %}评价{{ inspection_type_name }}照片{% endblock %}

{% block styles %}
<style>
    body {
        background-color: #f8f9fc;
    }

    .header-banner {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 20px 0;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .school-logo {
        max-height: 80px;
        margin-right: 15px;
    }

    .platform-name {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .school-name {
        font-size: 1.2rem;
    }

    .photo-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        background-color: white;
    }

    .photo-card .card-header {
        background-color: #4e73df;
        color: white;
        font-weight: bold;
        padding: 15px;
    }

    .photo-card .card-body {
        padding: 20px;
    }

    .photo-container {
        position: relative;
        margin-bottom: 15px;
    }

    .inspection-photo {
        width: 100%;
        border-radius: 5px;
        cursor: pointer;
    }

    .photo-description {
        margin-top: 10px;
        font-style: italic;
        color: #5a5c69;
    }

    .rating-container {
        margin-top: 15px;
        padding: 15px;
        background-color: #f8f9fc;
        border-radius: 5px;
    }

    .simple-rating h6 {
        text-align: center;
        margin-bottom: 15px;
        color: #5a5c69;
    }

    .rating-buttons {
        display: flex;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
    }

    .rating-btn {
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        min-width: 80px;
    }

    .rating-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .rating-btn.btn-success {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    }

    .rating-btn.btn-warning {
        background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    }

    .rating-btn.btn-danger {
        background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
    }

    .current-rating {
        text-align: center;
        margin-top: 10px;
    }

    .star-rating {
        display: flex;
        justify-content: center;
        margin-bottom: 10px;
    }

    .star-rating .star {
        font-size: 2rem;
        color: #dddfeb;
        cursor: pointer;
        margin: 0 5px;
        transition: color 0.2s;
    }

    .star-rating .star.active {
        color: #f6c23e;
    }

    .rating-label {
        text-align: center;
        font-weight: bold;
        color: #5a5c69;
    }

    .submit-rating {
        background-color: #4e73df;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: bold;
        margin-top: 15px;
        width: 100%;
    }

    .submit-rating:hover {
        background-color: #2e59d9;
    }

    .success-message {
        display: none;
        background-color: #1cc88a;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-top: 15px;
        text-align: center;
        font-weight: bold;
    }

    .error-message {
        display: none;
        background-color: #e74a3b;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-top: 15px;
        text-align: center;
        font-weight: bold;
    }

    .modal-photo {
        max-width: 100%;
        max-height: 80vh;
    }

    .no-photos {
        text-align: center;
        padding: 50px 0;
        color: #5a5c69;
    }

    .footer {
        background-color: #f8f9fc;
        padding: 20px 0;
        margin-top: 30px;
        text-align: center;
        color: #5a5c69;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="header-banner">
    <div class="container">
        <div class="d-flex align-items-center">
            <img src="/static/img/logo.png" alt="Logo" class="school-logo">
            <div>
                <div class="platform-name">校园餐智慧食堂监管平台</div>
                <div class="school-name">{{ school.name }}</div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-12 mb-4">
            <h2 class="text-center">{{ school.name }} - {{ log.log_date.strftime('%Y-%m-%d') }} {{ inspection_type_name }}照片评价</h2>
            <p class="text-center text-muted">请为以下检查照片进行评分，您的评价将帮助我们改进食堂管理</p>
        </div>
    </div>

    {% if photos_by_item %}
        {% for item, photos in photos_by_item.items() %}
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="photo-card">
                        <div class="card-header">
                            <i class="fas fa-clipboard-check mr-2"></i> {{ item }}
                        </div>
                        <div class="card-body">
                            {% for photo in photos %}
                                <div class="photo-container" id="photo-container-{{ photo.id }}">
                                    <img src="{{ photo.file_path }}" class="inspection-photo"
                                         alt="{{ item }}" data-toggle="modal" data-target="#photoModal"
                                         data-photo-src="{{ photo.file_path }}" data-photo-item="{{ item }}">

                                    {% if photo.description %}
                                        <div class="photo-description">{{ photo.description }}</div>
                                    {% endif %}

                                    <div class="rating-container">
                                        <div class="simple-rating" data-photo-id="{{ photo.id }}">
                                            <h6>请评价这张照片：</h6>
                                            <div class="rating-buttons">
                                                <button class="btn btn-success rating-btn" data-rating="good" data-photo-id="{{ photo.id }}">
                                                    <i class="fas fa-thumbs-up"></i> 好
                                                </button>
                                                <button class="btn btn-warning rating-btn" data-rating="average" data-photo-id="{{ photo.id }}">
                                                    <i class="fas fa-meh"></i> 一般
                                                </button>
                                                <button class="btn btn-danger rating-btn" data-rating="poor" data-photo-id="{{ photo.id }}">
                                                    <i class="fas fa-thumbs-down"></i> 差
                                                </button>
                                            </div>
                                        </div>
                                        <div class="rating-result" id="rating-result-{{ photo.id }}" style="display: none;">
                                            <div class="alert alert-success">
                                                <i class="fas fa-check-circle"></i> 评价已提交，谢谢您的反馈！
                                            </div>
                                        </div>
                                        {% if photo.rating %}
                                        <div class="current-rating">
                                            <small class="text-muted">
                                                当前评价:
                                                {% if photo.rating >= 4 %}
                                                    <span class="text-success"><i class="fas fa-thumbs-up"></i> 好</span>
                                                {% elif photo.rating >= 3 %}
                                                    <span class="text-warning"><i class="fas fa-meh"></i> 一般</span>
                                                {% else %}
                                                    <span class="text-danger"><i class="fas fa-thumbs-down"></i> 差</span>
                                                {% endif %}
                                            </small>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% if not loop.last %}<hr>{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="photo-card">
                    <div class="card-body">
                        <div class="no-photos">
                            <i class="fas fa-image fa-3x mb-3"></i>
                            <h4>暂无照片</h4>
                            <p>当前检查记录暂无照片可供评价</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- 照片查看模态框 -->
<div class="modal fade" id="photoModal" tabindex="-1" role="dialog" aria-labelledby="photoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoModalLabel">检查照片</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img src="" class="modal-photo" id="modalPhoto">
            </div>
        </div>
    </div>
</div>

<div class="footer">
    <div class="container">
        <p>© {{ now.year }} 校园餐智慧食堂监管平台 - 版权所有</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 照片模态框
    $('#photoModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const photoSrc = button.data('photo-src');
        const photoItem = button.data('photo-item');

        const modal = $(this);
        modal.find('.modal-title').text(photoItem + ' - 照片查看');
        modal.find('#modalPhoto').attr('src', photoSrc);
    });

    // 简单评价按钮点击事件
    $('.rating-btn').on('click', function() {
        const photoId = $(this).data('photo-id');
        const ratingType = $(this).data('rating');

        // 将评价类型转换为数字评分
        let rating;
        switch(ratingType) {
            case 'good':
                rating = 5;
                break;
            case 'average':
                rating = 3;
                break;
            case 'poor':
                rating = 1;
                break;
            default:
                rating = 3;
        }

        // 禁用按钮防止重复点击
        $(this).prop('disabled', true);
        $(this).siblings('.rating-btn').prop('disabled', true);

        // 发送评分请求
        fetch('/daily-management/api/v2/photos/public/rate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                photo_id: photoId,
                rating: rating
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 隐藏评价按钮，显示成功信息
                $(`#photo-container-${photoId} .simple-rating`).hide();
                $(`#rating-result-${photoId}`).show();

                // 更新当前评价显示
                let ratingText = '';
                let ratingClass = '';
                let ratingIcon = '';

                if (rating >= 4) {
                    ratingText = '好';
                    ratingClass = 'text-success';
                    ratingIcon = 'fas fa-thumbs-up';
                } else if (rating >= 3) {
                    ratingText = '一般';
                    ratingClass = 'text-warning';
                    ratingIcon = 'fas fa-meh';
                } else {
                    ratingText = '差';
                    ratingClass = 'text-danger';
                    ratingIcon = 'fas fa-thumbs-down';
                }

                $(`#photo-container-${photoId} .current-rating`).html(`
                    <small class="text-muted">
                        当前评价: <span class="${ratingClass}"><i class="${ratingIcon}"></i> ${ratingText}</span>
                    </small>
                `).show();

            } else {
                alert(data.error || '评价提交失败，请重试');
                // 重新启用按钮
                $(this).prop('disabled', false);
                $(this).siblings('.rating-btn').prop('disabled', false);
            }
        })
        .catch(error => {
            alert('评价提交失败，请重试');
            console.error('Error:', error);
            // 重新启用按钮
            $(this).prop('disabled', false);
            $(this).siblings('.rating-btn').prop('disabled', false);
        });
    });
</script>
{% endblock %}
